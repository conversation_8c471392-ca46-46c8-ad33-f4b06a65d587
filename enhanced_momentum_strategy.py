"""
Enhanced Momentum Strategy with GMGN.ai Integration
Combines historical CSV data with real-time GMGN.ai smart money tracking
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from gmgn_integration import GMGNIntegration

class EnhancedMomentumStrategy:
    def __init__(self, initial_capital=100000, max_positions=8, position_size=0.12):
        """
        Enhanced Momentum Strategy with GMGN.ai Integration
        
        Args:
            initial_capital: Starting capital in USD
            max_positions: Maximum number of concurrent positions
            position_size: Fraction of capital per position
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_positions = max_positions
        self.position_size = position_size
        self.positions = {}
        self.trade_history = []
        self.performance_metrics = {}
        
        # Enhanced strategy parameters
        self.momentum_threshold = 0.03  # 3% momentum threshold
        self.stop_loss = 0.08  # 8% stop loss (tighter)
        self.take_profit = 0.25  # 25% take profit
        self.min_liquidity = 50000  # Higher liquidity requirement
        self.lookback_period = 3  # Shorter lookback for crypto
        
        # GMGN.ai integration
        self.gmgn = GMGNIntegration()
        self.smart_money_weight = 0.4  # Weight for smart money signals
        self.trend_weight = 0.3  # Weight for trending tokens
        self.technical_weight = 0.3  # Weight for technical analysis
        
        # Smart money criteria
        self.min_smart_money_score = 7.0  # Minimum smart money score
        self.min_confidence_score = 0.7  # Minimum signal confidence
        
    def get_gmgn_enhanced_signals(self):
        """Get enhanced signals from GMGN.ai integration"""
        try:
            # Get smart money signals
            smart_signals = self.gmgn.get_smart_money_signals()
            
            # Get trending tokens
            trending_tokens = self.gmgn.get_trending_tokens(20)
            
            # Get market sentiment
            market_sentiment = self.gmgn.get_market_sentiment()
            
            # Get wallet analysis
            wallet_analysis = self.gmgn.get_wallet_analysis()
            
            return {
                'smart_signals': smart_signals,
                'trending_tokens': trending_tokens,
                'market_sentiment': market_sentiment,
                'wallet_analysis': wallet_analysis
            }
            
        except Exception as e:
            print(f"Error getting GMGN signals: {e}")
            return {
                'smart_signals': [],
                'trending_tokens': [],
                'market_sentiment': {'sentiment': 'neutral', 'confidence': 0.5},
                'wallet_analysis': None
            }
    
    def calculate_enhanced_momentum_score(self, token_symbol, price_data, gmgn_data):
        """Calculate enhanced momentum score combining technical and GMGN data"""
        if len(price_data) < self.lookback_period:
            return 0
        
        # Technical momentum (from original strategy)
        recent_prices = price_data['price'].tail(self.lookback_period)
        price_momentum = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
        
        # Volume momentum
        recent_volumes = price_data['usd_value'].tail(self.lookback_period)
        volume_momentum = recent_volumes.mean() / (recent_volumes.std() + 1e-8)
        
        # Trend consistency
        returns = recent_prices.pct_change().dropna()
        positive_streak = 0
        for ret in returns.iloc[::-1]:
            if ret > 0:
                positive_streak += 1
            else:
                break
        trend_consistency = positive_streak / len(returns) if len(returns) > 0 else 0
        
        # Technical score
        technical_score = (
            price_momentum * 0.5 +
            np.tanh(volume_momentum) * 0.3 +
            trend_consistency * 0.2
        )
        
        # GMGN.ai enhancements
        smart_money_score = 0
        trending_score = 0
        
        # Check if token is in smart money signals
        for signal in gmgn_data.get('smart_signals', []):
            if (signal['token_symbol'] == token_symbol and 
                signal['action'] == 'BUY' and
                signal['confidence_score'] >= self.min_confidence_score):
                smart_money_score = signal['confidence_score']
                break
        
        # Check if token is trending
        for trending_token in gmgn_data.get('trending_tokens', []):
            if trending_token['symbol'] == token_symbol:
                # Higher score for tokens with high smart money activity
                if trending_token['smart_money_activity'] == 'very_high':
                    trending_score = 0.9
                elif trending_token['smart_money_activity'] == 'high':
                    trending_score = 0.7
                elif trending_token['smart_money_activity'] == 'medium':
                    trending_score = 0.5
                break
        
        # Market sentiment adjustment
        sentiment_multiplier = 1.0
        market_sentiment = gmgn_data.get('market_sentiment', {})
        if market_sentiment.get('sentiment') == 'bullish':
            sentiment_multiplier = 1.0 + (market_sentiment.get('confidence', 0) * 0.2)
        elif market_sentiment.get('sentiment') == 'bearish':
            sentiment_multiplier = 1.0 - (market_sentiment.get('confidence', 0) * 0.2)
        
        # Combined enhanced score
        enhanced_score = (
            technical_score * self.technical_weight +
            smart_money_score * self.smart_money_weight +
            trending_score * self.trend_weight
        ) * sentiment_multiplier
        
        return enhanced_score
    
    def should_enter_position(self, token_symbol, price_data, current_time, gmgn_data):
        """Enhanced entry conditions with GMGN.ai integration"""
        # Basic checks
        if token_symbol in self.positions:
            return False
        
        if len(self.positions) >= self.max_positions:
            return False
        
        if len(price_data) < self.lookback_period:
            return False
        
        # Liquidity check
        latest_data = price_data.iloc[-1]
        if latest_data.get('liquidity', 0) < self.min_liquidity:
            return False
        
        # Get GMGN data if not provided
        if not gmgn_data:
            gmgn_data = self.get_gmgn_enhanced_signals()
        
        # Calculate enhanced momentum score
        enhanced_score = self.calculate_enhanced_momentum_score(token_symbol, price_data, gmgn_data)
        
        # Enhanced entry conditions
        entry_conditions = [
            enhanced_score > self.momentum_threshold,  # Basic momentum
            self.check_smart_money_alignment(token_symbol, gmgn_data),  # Smart money alignment
            self.check_security_requirements(token_symbol),  # Security checks
            self.check_market_conditions(gmgn_data)  # Market conditions
        ]
        
        return all(entry_conditions)
    
    def check_smart_money_alignment(self, token_symbol, gmgn_data):
        """Check if smart money is aligned with our position"""
        wallet_analysis = gmgn_data.get('wallet_analysis')
        if not wallet_analysis:
            return True  # Neutral if no data
        
        # Check if the tracked wallet has good performance
        smart_money_score = wallet_analysis.get('smart_money_score', 0)
        win_rate = wallet_analysis.get('pnl_30d', {}).get('win_rate', 0)
        
        # Check if token is in smart money's preferred tokens
        preferred_tokens = wallet_analysis.get('trading_patterns', {}).get('preferred_tokens', [])
        
        return (smart_money_score >= self.min_smart_money_score and 
                win_rate >= 0.6 and
                (token_symbol in preferred_tokens or len(preferred_tokens) == 0))
    
    def check_security_requirements(self, token_symbol):
        """Check token security using GMGN.ai data"""
        try:
            # In a real implementation, we would get the token address
            # For now, assume basic security checks pass
            return True
        except:
            return False
    
    def check_market_conditions(self, gmgn_data):
        """Check overall market conditions"""
        market_sentiment = gmgn_data.get('market_sentiment', {})
        sentiment = market_sentiment.get('sentiment', 'neutral')
        confidence = market_sentiment.get('confidence', 0.5)
        
        # Only trade in neutral or bullish markets with reasonable confidence
        return sentiment in ['neutral', 'bullish'] or confidence < 0.7
    
    def should_exit_position(self, token_symbol, current_price, current_time, gmgn_data):
        """Enhanced exit conditions with GMGN.ai integration"""
        if token_symbol not in self.positions:
            return False, None
        
        position = self.positions[token_symbol]
        entry_price = position['entry_price']
        current_return = (current_price - entry_price) / entry_price
        
        # Basic exit conditions
        if current_return <= -self.stop_loss:
            return True, "stop_loss"
        
        if current_return >= self.take_profit:
            return True, "take_profit"
        
        # Time-based exit
        days_held = (current_time - position['entry_time']).days
        if days_held >= 20:  # Shorter holding period for momentum
            return True, "time_exit"
        
        # Smart money exit signals
        for signal in gmgn_data.get('smart_signals', []):
            if (signal['token_symbol'] == token_symbol and 
                signal['action'] == 'SELL' and
                signal['confidence_score'] >= 0.8):
                return True, "smart_money_exit"
        
        # Market sentiment exit
        market_sentiment = gmgn_data.get('market_sentiment', {})
        if (market_sentiment.get('sentiment') == 'bearish' and 
            market_sentiment.get('confidence', 0) > 0.8):
            return True, "market_sentiment_exit"
        
        return False, None
    
    def enter_position(self, token_symbol, price, timestamp, token_data, gmgn_data=None):
        """Enter a new position with enhanced logging"""
        position_value = self.current_capital * self.position_size
        shares = position_value / price
        
        position = {
            'symbol': token_symbol,
            'entry_price': price,
            'entry_time': timestamp,
            'shares': shares,
            'position_value': position_value,
            'entry_data': token_data,
            'gmgn_data': gmgn_data,
            'entry_reason': 'enhanced_momentum'
        }
        
        self.positions[token_symbol] = position
        self.current_capital -= position_value
        
        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'BUY',
            'price': price,
            'shares': shares,
            'value': position_value,
            'capital_remaining': self.current_capital,
            'strategy': 'enhanced_momentum',
            'gmgn_score': gmgn_data.get('wallet_analysis', {}).get('smart_money_score', 0) if gmgn_data else 0
        }
        self.trade_history.append(trade)
        
        return position
    
    def exit_position(self, token_symbol, price, timestamp, exit_reason):
        """Exit position with enhanced tracking"""
        if token_symbol not in self.positions:
            return None
        
        position = self.positions[token_symbol]
        shares = position['shares']
        exit_value = shares * price
        
        profit_loss = exit_value - position['position_value']
        return_pct = profit_loss / position['position_value']
        
        self.current_capital += exit_value
        
        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'SELL',
            'price': price,
            'shares': shares,
            'value': exit_value,
            'profit_loss': profit_loss,
            'return_pct': return_pct,
            'exit_reason': exit_reason,
            'capital_remaining': self.current_capital,
            'days_held': (timestamp - position['entry_time']).days,
            'strategy': 'enhanced_momentum',
            'entry_gmgn_score': position.get('gmgn_data', {}).get('wallet_analysis', {}).get('smart_money_score', 0)
        }
        self.trade_history.append(trade)
        
        del self.positions[token_symbol]
        return trade
    
    def run_backtest(self, price_data_dict, start_date=None, end_date=None):
        """Run enhanced momentum strategy backtest"""
        print("Running Enhanced Momentum Strategy Backtest with GMGN.ai Integration...")
        
        # Get GMGN data once for the backtest period
        gmgn_data = self.get_gmgn_enhanced_signals()
        
        # Create timeline
        all_updates = []
        for token, data in price_data_dict.items():
            for _, row in data.iterrows():
                all_updates.append({
                    'timestamp': row['datetime'],
                    'token': token,
                    'price': row['price'],
                    'data': row
                })
        
        all_updates = sorted(all_updates, key=lambda x: x['timestamp'])
        
        if start_date:
            all_updates = [u for u in all_updates if u['timestamp'] >= start_date]
        if end_date:
            all_updates = [u for u in all_updates if u['timestamp'] <= end_date]
        
        print(f"Processing {len(all_updates)} price updates with GMGN.ai integration...")
        
        for i, update in enumerate(all_updates):
            token = update['token']
            price = update['price']
            timestamp = update['timestamp']
            
            token_history = price_data_dict[token]
            current_history = token_history[token_history['datetime'] <= timestamp]
            
            # Check exit conditions
            if token in self.positions:
                should_exit, exit_reason = self.should_exit_position(
                    token, price, timestamp, gmgn_data
                )
                if should_exit:
                    self.exit_position(token, price, timestamp, exit_reason)
            
            # Check entry conditions
            elif self.should_enter_position(token, current_history, timestamp, gmgn_data):
                self.enter_position(token, price, timestamp, update['data'], gmgn_data)
            
            if i % 1000 == 0:
                print(f"Processed {i}/{len(all_updates)} updates. Capital: ${self.current_capital:.2f}")
        
        # Close remaining positions
        final_timestamp = all_updates[-1]['timestamp'] if all_updates else datetime.now()
        for token in list(self.positions.keys()):
            final_price = price_data_dict[token].iloc[-1]['price']
            self.exit_position(token, final_price, final_timestamp, "backtest_end")
        
        print(f"Enhanced backtest complete. Final capital: ${self.current_capital:.2f}")
        return self.calculate_performance_metrics()
    
    def calculate_performance_metrics(self):
        """Calculate enhanced performance metrics"""
        if not self.trade_history:
            return {}
        
        trades_df = pd.DataFrame(self.trade_history)
        sell_trades = trades_df[trades_df['action'] == 'SELL'].copy()
        
        if len(sell_trades) == 0:
            return {'error': 'No completed trades'}
        
        # Basic metrics
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        total_trades = len(sell_trades)
        winning_trades = len(sell_trades[sell_trades['profit_loss'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Enhanced metrics
        avg_profit = sell_trades['profit_loss'].mean()
        total_profit = sell_trades['profit_loss'].sum()
        avg_return = sell_trades['return_pct'].mean()
        
        # Risk metrics
        returns = sell_trades['return_pct']
        volatility = returns.std()
        sharpe_ratio = (avg_return / volatility) if volatility > 0 else 0
        
        # GMGN-specific metrics
        gmgn_trades = sell_trades[sell_trades['entry_gmgn_score'] > 0]
        gmgn_performance = gmgn_trades['return_pct'].mean() if len(gmgn_trades) > 0 else 0
        
        self.performance_metrics = {
            'total_return': total_return,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit_per_trade': avg_profit,
            'avg_return_per_trade': avg_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'avg_days_held': sell_trades['days_held'].mean(),
            'gmgn_enhanced_trades': len(gmgn_trades),
            'gmgn_performance': gmgn_performance
        }
        
        return self.performance_metrics
    
    def print_performance_report(self):
        """Print enhanced performance report"""
        if not self.performance_metrics:
            self.calculate_performance_metrics()
        
        metrics = self.performance_metrics
        
        print("\n" + "="*60)
        print("ENHANCED MOMENTUM STRATEGY PERFORMANCE REPORT")
        print("="*60)
        
        print(f"Initial Capital: ${self.initial_capital:,.2f}")
        print(f"Final Capital: ${metrics.get('final_capital', 0):,.2f}")
        print(f"Total Return: {metrics.get('total_return', 0)*100:.2f}%")
        print(f"Total Profit: ${metrics.get('total_profit', 0):,.2f}")
        
        print(f"\nTrading Statistics:")
        print(f"Total Trades: {metrics.get('total_trades', 0)}")
        print(f"Win Rate: {metrics.get('win_rate', 0)*100:.1f}%")
        print(f"Average Profit per Trade: ${metrics.get('avg_profit_per_trade', 0):.2f}")
        print(f"Average Return per Trade: {metrics.get('avg_return_per_trade', 0)*100:.2f}%")
        print(f"Average Days Held: {metrics.get('avg_days_held', 0):.1f}")
        
        print(f"\nGMGN.ai Integration Results:")
        print(f"GMGN-Enhanced Trades: {metrics.get('gmgn_enhanced_trades', 0)}")
        print(f"GMGN-Enhanced Performance: {metrics.get('gmgn_performance', 0)*100:.2f}%")
        
        print(f"\nRisk Metrics:")
        print(f"Volatility: {metrics.get('volatility', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")
        
        return metrics
